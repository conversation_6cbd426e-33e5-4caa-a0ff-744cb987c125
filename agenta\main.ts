// agenta_adapter.ts
// Single-file Deno implementation of the Agenta OpenAI API Adapter.
// Minimal external libraries used. Run with: deno run --allow-net agenta_adapter.ts

import { serve } from "https://deno.land/std@0.200.0/http/server.ts";

// -------------------- Configuration --------------------

const AGENTA_CHAT_URL = "https://cloud.agenta.ai/services/chat/test";
const AGENTA_BILLING_URL = "https://cloud.agenta.ai/api/billing/usage";
const AGENTA_REFRESH_URL = "https://cloud.agenta.ai/api/auth/session/refresh";

const MIN_BALANCE_THRESHOLD = 100;
const MAX_CONSECUTIVE_FAILURES = 3;
const KEY_RECHECK_INTERVAL = 3600_000; // 1 hour in ms
const BALANCE_REFRESH_INTERVAL = 600_000; // 10 minutes in ms
const TOKEN_REFRESH_INTERVAL = 60_000; // 1 minute in ms

// -------------------- Hardcoded Models --------------------

const MODELS = {
  "Anthropic": [
    "anthropic/claude-sonnet-4-20250514",
    "anthropic/claude-opus-4-20250514",
    "anthropic/claude-3-7-sonnet-20250219",
    "anthropic/claude-3-5-sonnet-20241022",
    "anthropic/claude-3-5-sonnet-20240620",
    "anthropic/claude-3-5-haiku-20241022",
    "anthropic/claude-3-opus-20240229",
    "anthropic/claude-3-sonnet-20240229",
    "anthropic/claude-3-haiku-20240307",
    "anthropic/claude-2.1",
    "anthropic/claude-2"
  ],
  "Cohere": [
    "cohere/command-light",
    "cohere/command-r-plus",
    "cohere/command-nightly"
  ],
  "DeepInfra": [
    "deepinfra/meta-llama/Llama-2-70b-chat-hf",
    "deepinfra/meta-llama/Llama-2-13b-chat-hf",
    "deepinfra/codellama/CodeLlama-34b-Instruct-hf",
    "deepinfra/mistralai/Mistral-7B-Instruct-v0.1"
  ],
  "Gemini": [
    "gemini/gemini-2.5-flash-preview-04-17",
    "gemini/gemini-2.5-pro-exp-03-25",
    "gemini/gemini-2.0-flash-001",
    "gemini/gemini-2.0-flash-lite-preview-02-05",
    "gemini/gemini-1.5-pro-latest",
    "gemini/gemini-1.5-flash",
    "gemini/gemini-1.5-flash-8b",
    "gemini/gemma-3-27b-it"
  ],
  "Groq": [
    "groq/deepseek-r1-distill-llama-70b",
    "groq/deepseek-r1-distill-llama-70b-specdec",
    "groq/gemma2-9b-it",
    "groq/llama-3.1-8b-instant",
    "groq/llama-3.2-11b-vision-preview",
    "groq/llama-3.2-1b-preview",
    "groq/llama-3.2-3b-preview",
    "groq/llama-3.2-90b-vision-preview",
    "groq/llama-3.3-70b-specdec",
    "groq/llama-3.3-70b-versatile",
    "groq/llama-guard-3-8b",
    "groq/llama3-70b-8192",
    "groq/llama3-8b-8192",
    "groq/mixtral-8x7b-32768"
  ],
  "Mistral": [
    "mistral/mistral-tiny",
    "mistral/mistral-small",
    "mistral/mistral-medium",
    "mistral/mistral-large-latest"
  ],
  "OpenAI": [
    "gpt-4.5-preview",
    "gpt-3.5-turbo-1106",
    "gpt-3.5-turbo",
    "gpt-4",
    "gpt-4o",
    "gpt-4o-mini",
    "gpt-4-1106-preview",
    "gpt-4.1",
    "gpt-4.1-mini",
    "gpt-4.1-nano",
    "o4-mini"
  ],
  "OpenRouter": [
    "openrouter/qwen/qwen3-235b-a22b",
    "openrouter/qwen/qwen3-32b",
    "openrouter/qwen/qwen3-30b-a3b",
    "openrouter/meta-llama/llama-4-maverick",
    "openrouter/meta-llama/llama-4-scout",
    "openrouter/anthropic/claude-3-opus",
    "openrouter/anthropic/claude-3-sonnet",
    "openrouter/anthropic/claude-3.5-haiku",
    "openrouter/anthropic/claude-3.5-haiku-20241022",
    "openrouter/anthropic/claude-3.5-haiku-20241022:beta",
    "openrouter/anthropic/claude-3.5-haiku:beta",
    "openrouter/anthropic/claude-3.5-sonnet",
    "openrouter/anthropic/claude-3.7-sonnet",
    "openrouter/anthropic/claude-3.5-sonnet-20240620",
    "openrouter/anthropic/claude-3.5-sonnet-20240620:beta",
    "openrouter/anthropic/claude-3.5-sonnet:beta",
    "openrouter/cohere/command",
    "openrouter/cohere/command-r",
    "openrouter/cohere/command-r-03-2024",
    "openrouter/cohere/command-r-08-2024",
    "openrouter/cohere/command-r-plus",
    "openrouter/cohere/command-r-plus-04-2024",
    "openrouter/cohere/command-r-plus-08-2024",
    "openrouter/cohere/command-r7b-12-2024",
    "openrouter/deepseek/deepseek-chat",
    "openrouter/deepseek/deepseek-chat-v2.5",
    "openrouter/deepseek/deepseek-r1",
    "openrouter/deepseek/deepseek-r1:nitro",
    "openrouter/deepseek/deepseek-r1-distill-llama-70b",
    "openrouter/deepseek/deepseek-r1-distill-qwen-1.5b",
    "openrouter/deepseek/deepseek-r1-distill-qwen-14b",
    "openrouter/deepseek/deepseek-r1-distill-qwen-32b",
    "openrouter/databricks/dbrx-instruct",
    "openrouter/google/gemini-2.0-flash-001",
    "openrouter/google/gemini-2.0-flash-exp:free",
    "openrouter/google/gemini-2.0-flash-lite-preview-02-05:free",
    "openrouter/google/gemini-2.0-flash-thinking-exp-1219:free",
    "openrouter/google/gemini-2.0-flash-thinking-exp:free",
    "openrouter/google/gemini-2.0-pro-exp-02-05:free",
    "openrouter/google/gemini-exp-1206:free",
    "openrouter/google/gemini-flash-1.5",
    "openrouter/google/gemini-flash-1.5-8b",
    "openrouter/google/gemini-flash-1.5-8b-exp",
    "openrouter/google/gemini-pro",
    "openrouter/google/gemini-pro-1.5",
    "openrouter/google/gemini-pro-vision",
    "openrouter/google/palm-2-chat-bison",
    "openrouter/google/palm-2-codechat-bison",
    "openrouter/meta-llama/llama-3.3-70b-instruct",
    "openrouter/meta-llama/llama-3.2-90b-vision-instruct",
    "openrouter/meta-llama/llama-3.1-405b-instruct",
    "openrouter/mistralai/mistral-large",
    "openrouter/mistralai/mistral-medium",
    "openrouter/mistralai/mistral-small",
    "openrouter/mistralai/mixtral-8x7b-instruct",
    "openrouter/nousresearch/hermes-3-llama-3.1-405b",
    "openrouter/nousresearch/hermes-3-llama-3.1-70b",
    "openrouter/nousresearch/nous-hermes-2-mixtral-8x7b-dpo",
    "openrouter/qwen/qwen-max",
    "openrouter/qwen/qwen-plus",
    "openrouter/qwen/qwen-2.5-72b-instruct",
    "openrouter/qwen/qwen-2.5-coder-32b-instruct",
    "openrouter/x-ai/grok-2",
    "openrouter/x-ai/grok-2-1212",
    "openrouter/x-ai/grok-2-vision-1212",
    "openrouter/x-ai/grok-vision-beta",
    "openrouter/google/gemini-2.0-flash-001",
    "openrouter/perplexity/sonar-reasoning"
  ],
  "Perplexity AI": [
    "perplexity/sonar",
    "perplexity/sonar-pro",
    "perplexity/sonar-reasoning",
    "perplexity/sonar-reasoning-pro"
  ],
  "togetherai": [
    "together_ai/deepseek-ai/DeepSeek-R1",
    "together_ai/deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
    "together_ai/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B",
    "together_ai/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
    "together_ai/deepseek-ai/DeepSeek-V3",
    "together_ai/meta-llama/Llama-3.3-70B-Instruct-Turbo",
    "together_ai/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
    "together_ai/meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
    "together_ai/meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
    "together_ai/meta-llama/Meta-Llama-3-8B-Instruct-Turbo",
    "together_ai/meta-llama/Meta-Llama-3-70B-Instruct-Turbo",
    "together_ai/meta-llama/Llama-3.2-3B-Instruct-Turbo",
    "together_ai/meta-llama/Meta-Llama-3-8B-Instruct-Lite",
    "together_ai/meta-llama/Meta-Llama-3-70B-Instruct-Lite",
    "together_ai/meta-llama/Llama-3-8b-chat-hf",
    "together_ai/meta-llama/Llama-3-70b-chat-hf",
    "together_ai/nvidia/Llama-3.1-Nemotron-70B-Instruct-HF",
    "together_ai/Qwen/Qwen2.5-Coder-32B-Instruct",
    "together_ai/Qwen/QwQ-32B-Preview",
    "together_ai/microsoft/WizardLM-2-8x22B",
    "together_ai/google/gemma-2-27b-it",
    "together_ai/google/gemma-2-9b-it",
    "together_ai/databricks/dbrx-instruct",
    "together_ai/google/gemma-2b-it",
    "together_ai/Gryphe/MythoMax-L2-13b",
    "together_ai/meta-llama/Llama-2-13b-chat-hf",
    "together_ai/mistralai/Mistral-Small-24B-Instruct-2501",
    "together_ai/mistralai/Mistral-7B-Instruct-v0.1",
    "together_ai/mistralai/Mistral-7B-Instruct-v0.2",
    "together_ai/mistralai/Mistral-7B-Instruct-v0.3",
    "together_ai/mistralai/Mixtral-8x7B-Instruct-v0.1",
    "together_ai/mistralai/Mixtral-8x22B-Instruct-v0.1",
    "together_ai/NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO",
    "together_ai/Qwen/Qwen2.5-7B-Instruct-Turbo",
    "together_ai/Qwen/Qwen2.5-72B-Instruct-Turbo",
    "together_ai/Qwen/Qwen2-72B-Instruct",
    "together_ai/Qwen/Qwen2-VL-72B-Instruct",
    "together_ai/upstage/SOLAR-10.7B-Instruct-v1.0"
  ]
};

// -------------------- Hardcoded API Keys --------------------

// Client API keys (bearer tokens accepted for incoming requests)
const VALID_CLIENT_KEYS = new Set([
  "sk-fmai",
  "sk-agenta-22222222222222222222222222222222"
]);

// Remote Agenta keys (replace with real tokens in production)
interface AgentaKey {
  authToken: string;
  refreshToken: string;
  projectId: string;
  appId: string;
  balance: number;
  failures: number;
  lastChecked: number;
  lastUsed: number;
  isFunctional: boolean;
}

let agentaKeys: AgentaKey[] = [
  {
    authToken: "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    refreshToken: "TyJJr1bTV4DO7NOFc2FePdWQbpgeAqaka1PY%2B6FYxzH9qtJZJguA0NUWjJDpsXuiyprDNyrMBzgxQNt/sEN5CtoLC3gq6p5/lft%2Bfd9HFpOv7gL5kSwDwgK0x0k5qWMPAV8Glph53NorIlW26rBDUX5LdAjrrbmzzTJgTaEsA/nlIg8mw%2BlFN9XXls2pOuvDDKHKYHi7Bh%2BmksQ/FRUxkN9u8X5yg8bVYWht1w7TzwxAD3mb4d384D3XY3gfNsJ%2BfCUYIr/Ps%2B1No6rvDmsoBANK28OUhhx1T8ae3pXv8zpDzdiKrMYeF47knLnSiuOYMY0q6VEZDib8Y/radHriMFAwchTBfWyT4SEjItun2Zj/mqpmGzvDWRq55aZ40nUeKfoiP5hbjv89VOgM.c7b6b2e933a57aab3a22bc10625ec5a9f7c4ed7af51e30e85fbd680e769381b3.V2",
    projectId: "019743c0-393d-7da2-90ba-df2ebb2abec6",
    appId: "019743c0-cbbb-7cd2-9a73-52f35499d949",
    balance: 0,
    failures: 0,
    lastChecked: 0,
    lastUsed: 0,
    isFunctional: false
  }
];

let currentKeyIndex = 0;

// -------------------- Utility Functions --------------------

function nowMillis(): number {
  return Date.now();
}

function secondsSince(tsMillis: number): number {
  return (nowMillis() - tsMillis) / 1000;
}

function makeUUID(): string {
  // Use Web Crypto API
  return crypto.randomUUID();
}

// -------------------- Agenta Key Management --------------------

async function refreshKeyTokens(key: AgentaKey): Promise<boolean> {
  try {
    const headers = new Headers({
      "User-Agent": "Deno",
      "Accept": "application/json, text/plain, */*",
      "Content-Type": "application/json",
      "Cookie": `sRefreshToken="${key.refreshToken}"; sAccessToken="${key.authToken}"`
    });
    const resp = await fetch(AGENTA_REFRESH_URL, {
      method: "POST",
      headers,
    });
    if (resp.status === 200) {
      const newAuthToken = resp.headers.get("st-access-token");
      const setCookie = resp.headers.get("set-cookie") || "";
      // Extract sRefreshToken from set-cookie header
      const match = setCookie.match(/sRefreshToken="([^"]+)"/);
      if (newAuthToken && match) {
        key.authToken = newAuthToken;
        key.refreshToken = match[1];
        key.lastUsed = nowMillis();
        key.failures = 0;
        return true;
      }
    }
    return false;
  } catch {
    return false;
  }
}

async function checkKeyBalance(key: AgentaKey, autoRefresh = false): Promise<boolean> {
  try {
    const url = `${AGENTA_BILLING_URL}?project_id=${key.projectId}`;
    const headers = new Headers({
      "Cookie": `sAccessToken=${key.authToken}`,
    });
    const resp = await fetch(url, { headers });
    if (resp.status === 200) {
      const data = await resp.json();
      key.balance = data.traces?.free || 0;
      key.lastChecked = nowMillis();
      key.isFunctional = key.balance >= MIN_BALANCE_THRESHOLD;
      key.failures = 0;
      if (autoRefresh && key.isFunctional) {
        const refreshed = await refreshKeyTokens(key);
        if (refreshed) {
          saveAgentaKeys(); // In-memory no-op in this single-file version
        }
      }
      return true;
    } else if (resp.status === 401 || resp.status === 403) {
      // Attempt to refresh token once
      const ok = await refreshKeyTokens(key);
      if (ok) {
        return await checkKeyBalance(key, autoRefresh);
      } else {
        key.isFunctional = false;
        key.balance = 0;
        return false;
      }
    } else {
      key.failures += 1;
      if (key.failures >= MAX_CONSECUTIVE_FAILURES) {
        key.isFunctional = false;
      }
      return false;
    }
  } catch {
    key.failures += 1;
    if (key.failures >= MAX_CONSECUTIVE_FAILURES) {
      key.isFunctional = false;
    }
    return false;
  }
}

async function initialCheckKeys() {
  if (agentaKeys.length === 0) return;
  await Promise.all(agentaKeys.map((k) => checkKeyBalance(k)));
}

function getNextFunctionalKey(): AgentaKey | null {
  const functional = agentaKeys.filter((k) => k.isFunctional);
  if (functional.length === 0) return null;
  const key = functional[currentKeyIndex % functional.length];
  currentKeyIndex = (currentKeyIndex + 1) % functional.length;
  key.lastUsed = nowMillis();
  return key;
}

function updateKeyOnFailure(key: AgentaKey, isAuthError = false) {
  if (isAuthError) {
    key.isFunctional = false;
    key.balance = 0;
  } else {
    key.failures += 1;
    if (key.failures >= MAX_CONSECUTIVE_FAILURES) {
      key.isFunctional = false;
    }
  }
}

function saveAgentaKeys() {
  // No-op in this single-file Deno version (no persistent storage)
}

// Background refresh of unused keys
function startBackgroundRefresh() {
  setInterval(async () => {
    const now = nowMillis();
    for (const key of [...agentaKeys]) {
      if (key.isFunctional && now - key.lastUsed > TOKEN_REFRESH_INTERVAL) {
        const ok = await refreshKeyTokens(key);
        if (!ok) {
          key.isFunctional = false;
          // Remove permanently failed key
          agentaKeys = agentaKeys.filter((k) => k !== key);
          console.warn(`Removed permanently failed key for project ${key.projectId}`);
        }
      }
    }
    saveAgentaKeys();
  }, TOKEN_REFRESH_INTERVAL);
}

// -------------------- HTTP Request/Response Schemas --------------------

interface ChatMessage {
  role: string;
  content: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

interface ModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

interface ModelList {
  object: string;
  data: ModelInfo[];
}

interface ChatCompletionChoice {
  message: ChatMessage;
  index: number;
  finish_reason: string;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface StreamChoice {
  delta: Record<string, any>;
  index: number;
  finish_reason?: string;
}

interface StreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: StreamChoice[];
}

// -------------------- Server and Routes --------------------

async function handler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  // Authentication middleware for /v1 routes
  if (path.startsWith("/v1/")) {
    const auth = req.headers.get("Authorization") || "";
    if (!auth.startsWith("Bearer ")) {
      return new Response(JSON.stringify({ detail: "Invalid API key." }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }
    const token = auth.slice(7);
    if (!VALID_CLIENT_KEYS.has(token)) {
      return new Response(JSON.stringify({ detail: "Invalid API key." }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }
  }

  // GET /v1/models
  if (method === "GET" && path === "/v1/models") {
    const list: ModelInfo[] = [];
    const now = Math.floor(nowMillis() / 1000);
    for (const [provider, modelIds] of Object.entries(MODELS)) {
      for (const mid of modelIds) {
        list.push({ id: mid, object: "model", created: now, owned_by: provider });
      }
    }
    const body: ModelList = { object: "list", data: list };
    return new Response(JSON.stringify(body), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }

  // GET /models (no auth)
  if (method === "GET" && path === "/models") {
    const list: ModelInfo[] = [];
    const now = Math.floor(nowMillis() / 1000);
    for (const [provider, modelIds] of Object.entries(MODELS)) {
      for (const mid of modelIds) {
        list.push({ id: mid, object: "model", created: now, owned_by: provider });
      }
    }
    const body: ModelList = { object: "list", data: list };
    return new Response(JSON.stringify(body), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }

  // POST /v1/chat/completions
  if (method === "POST" && path === "/v1/chat/completions") {
    let reqJson: ChatCompletionRequest;
    try {
      reqJson = await req.json();
    } catch {
      return new Response(JSON.stringify({ detail: "Invalid JSON." }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
    const { model, messages, stream = false, temperature, max_tokens, top_p } = reqJson;

    // Validate model exists
    let found = false;
    for (const arr of Object.values(MODELS)) {
      if (arr.includes(model)) {
        found = true;
        break;
      }
    }
    if (!found) {
      return new Response(JSON.stringify({ detail: `Model '${model}' not found.` }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Get next functional key
    const key = getNextFunctionalKey();
    if (!key) {
      return new Response(JSON.stringify({ detail: "No functional Agenta keys available." }), {
        status: 503,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Extract system message from messages array
    let systemContent = "You are a helpful assistant.";
    const userMessages: ChatMessage[] = [];
    for (const msg of messages) {
      if (msg.role === "system") {
        systemContent = msg.content;
      } else {
        userMessages.push({ role: msg.role, content: msg.content });
      }
    }

    // Build Agenta payload
    const agentaPayload = {
      ag_config: {
        prompt: {
          messages: [{ role: "system", content: `\n${systemContent}` }],
          template_format: "curly",
          input_keys: ["context"],
          llm_config: {
            model: model,
            max_tokens: max_tokens ?? 16384,
            top_p: top_p ?? 1.0,
            temperature: temperature ?? 0.1,
            tools: [],
          },
        },
      },
      inputs: { context: systemContent },
      messages: userMessages.map((m) => ({ role: m.role, content: m.content })),
    };

    const headers = new Headers({
      "Authorization": `Bearer ${key.authToken}`,
      "Content-Type": "application/json",
      "User-Agent": "Deno",
    });
    const urlChat = `${AGENTA_CHAT_URL}?project_id=${encodeURIComponent(key.projectId)}&application_id=${encodeURIComponent(key.appId)}`;

    // Function to call Agenta once (with current key tokens)
    async function callAgenta(): Promise<{ success: boolean; content?: string; status?: number; text?: string }> {
      try {
        const resp = await fetch(urlChat, {
          method: "POST",
          headers,
          body: JSON.stringify(agentaPayload),
        });
        if (resp.ok) {
          // Immediately attempt to refresh tokens
          const refreshed = await refreshKeyTokens(key);
          if (refreshed) {
            saveAgentaKeys();
          }
          const respText = await resp.text();
          let finalContent = "";
          for (const line of respText.split("\n")) {
            if (line.trim()) {
              try {
                const data = JSON.parse(line);
                if (data.data?.role === "assistant" && "content" in data.data) {
                  finalContent = data.data.content;
                }
              } catch {
                continue;
              }
            }
          }
          return { success: true, content: finalContent };
        } else {
          return { success: false, status: resp.status, text: await resp.text() };
        }
      } catch {
        return { success: false };
      }
    }

    // Make initial call
    let result = await callAgenta();
    if (!result.success && (result.status === 401 || result.status === 403)) {
      // Try refreshing and retry once
      const refreshed = await refreshKeyTokens(key);
      if (refreshed) {
        saveAgentaKeys();
        headers.set("Authorization", `Bearer ${key.authToken}`);
        result = await callAgenta();
        if (!result.success) {
          updateKeyOnFailure(key, true);
          return new Response(JSON.stringify({ detail: "Agenta API error after retry." }), {
            status: 503,
            headers: { "Content-Type": "application/json" },
          });
        }
      } else {
        updateKeyOnFailure(key, true);
        return new Response(JSON.stringify({ detail: "Invalid Agenta credentials." }), {
          status: 503,
          headers: { "Content-Type": "application/json" },
        });
      }
    } else if (!result.success) {
      updateKeyOnFailure(key);
      return new Response(JSON.stringify({ detail: `Agenta API error: ${result.text ?? "Unknown"}` }), {
        status: result.status || 500,
        headers: { "Content-Type": "application/json" },
      });
    }

    const content = result.content ?? "";

    if (stream) {
      // Stream as SSE
      const streamId = `chatcmpl-${makeUUID()}`;
      const created = Math.floor(nowMillis() / 1000);

      const encoder = new TextEncoder();
      const stream = new ReadableStream({
        async start(controller) {
          // Role delta
          const roleMsg: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created,
            model,
            choices: [{ delta: { role: "assistant" }, index: 0 }],
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(roleMsg)}\n\n`));

          // Content delta
          if (content) {
            const contentMsg: StreamResponse = {
              id: streamId,
              object: "chat.completion.chunk",
              created,
              model,
              choices: [{ delta: { content }, index: 0 }],
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(contentMsg)}\n\n`));
          }

          // Finish
          const finishMsg: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created,
            model,
            choices: [{ delta: {}, index: 0, finish_reason: "stop" }],
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(finishMsg)}\n\n`));
          controller.enqueue(encoder.encode("data: [DONE]\n\n"));
          controller.close();
        }
      });

      return new Response(stream, {
        status: 200,
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache, no-transform",
          "Connection": "keep-alive",
        },
      });
    } else {
      // Non-stream response
      const responseBody: ChatCompletionResponse = {
        id: `chatcmpl-${makeUUID()}`,
        object: "chat.completion",
        created: Math.floor(nowMillis() / 1000),
        model,
        choices: [
          {
            message: { role: "assistant", content },
            index: 0,
            finish_reason: "stop",
          },
        ],
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
      };
      return new Response(JSON.stringify(responseBody), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    }
  }

  // Fallback 404
  return new Response("Not Found", { status: 404 });
}

// -------------------- Startup --------------------

console.log("Starting Agenta OpenAI API Adapter (Deno Edition)...");
await initialCheckKeys();
startBackgroundRefresh();

serve(handler, { port: 8000 });
console.log("Server listening on http://0.0.0.0:8000/");