# AnyModel.xyz 自动注册脚本 - 详细日志版

## 🎉 全面升级完成

✅ 网络连接错误已修复
✅ SSL连接问题已解决
✅ 详细步骤日志已添加
✅ 固定邮箱监听已配置

## 🔧 最新修复内容

### 1. SSL连接问题修复
- ✅ 修复了 `[SSL: UNEXPECTED_EOF_WHILE_READING]` 错误
- ✅ 添加了自定义SSL上下文配置
- ✅ 禁用SSL验证避免协议冲突
- ✅ 增加了传输层重试机制

### 2. 详细日志系统
- ✅ 每个步骤都有详细的进度显示
- ✅ 网络请求的完整日志记录
- ✅ 错误信息的详细分类和说明
- ✅ 实时显示邮件检查过程

### 3. 固定邮箱监听
- ✅ 使用固定邮箱 `<EMAIL>`
- ✅ 所有验证邮件自动转发到此邮箱
- ✅ 避免临时邮箱不稳定问题
- ✅ 提高验证邮件获取成功率

### 4. 增强错误处理
- ✅ 区分SSL错误、超时错误、连接错误
- ✅ 每种错误都有针对性的重试策略
- ✅ 详细的异常类型和错误信息显示
- ✅ 智能重试间隔调整

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install httpx
```

### 2. 运行脚本
```bash
python anymodel_register.py
```

### 3. 选择模式
- **模式1**: 注册单个账户（测试用）
- **模式2**: 批量注册账户

### 4. 批量注册参数
- **账户数量**: 要注册的总数
- **延迟时间**: 建议10-15秒（避免限流）

## 📋 详细日志示例

```
AnyModel.xyz 自动注册工具
==============================
提示: 按 Ctrl+C 可以随时中断操作

注册模式:
1. 注册单个账户
2. 批量注册账户

选择模式 (1-2): 1

🔧 初始化 AnyModel 注册器...
📧 固定邮箱: <EMAIL>
✅ HTTP客户端初始化完成（已配置SSL兼容性）

============================================================
🚀 开始注册流程
============================================================

📝 步骤1: 生成注册邮箱
📧 注册邮箱: <EMAIL>
🔑 登录密码: <EMAIL>

📝 步骤2: 发起注册请求
🌐 请求URL: https://app.anymodel.xyz/api/signup

🔄 注册尝试 1/3
📤 发送注册请求...
   📧 邮箱: <EMAIL>
   🔑 密码: <EMAIL>
📊 响应状态: 201
✅ 注册成功!
   📝 服务器消息: User created, check your email for verification code
   📋 完整响应: {'msg': 'User created, check your email for verification code'}
✅ 步骤2完成: 注册请求成功

📝 步骤3: 获取验证邮件
📬 监听邮箱: <EMAIL>
💡 说明: 所有验证邮件都会转发到此固定邮箱
🔍 开始监听邮件: <EMAIL>
🌐 请求URL: https://tempmail.plus/api/mails?email=llsdgi%40mailto.plus&first_id=0&epin=
⏰ 最大尝试次数: 10, 间隔: 5秒

📡 第 1/10 次检查邮件...
📊 响应状态: 200
📋 响应数据: result=True, count=1
📬 收到 1 封邮件，开始筛选...
  📧 邮件1: ID=3347301430
      主题: Your AnyModel code
      发件人: <EMAIL>
✅ 找到目标验证邮件!
   📧 邮件ID: 3347301430
   📝 主题: Your AnyModel code
✅ 步骤3完成: 获取到邮件ID 3347301430

📝 步骤4: 提取验证码
🔄 获取邮件详情尝试 1/3
✅ 提取到验证码: hzue2l5v
✅ 步骤4完成: 验证码 hzue2l5v

📝 步骤5: 验证登录
🌐 请求URL: https://app.anymodel.xyz/api/login

🔄 登录尝试 1/3
📤 发送登录请求...
   📧 邮箱: <EMAIL>
   🔑 密码: <EMAIL>
   🔐 验证码: hzue2l5v
📊 响应状态: 201
✅ 登录成功!
   👤 用户ID: 684400aa4d32f615a4af3c35
   🎫 Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   📋 完整响应: {'id': '684400aa4d32f615a4af3c35', 'email': '<EMAIL>', 'token': '...'}
✅ 步骤5完成: 登录验证成功

🎉 注册流程全部完成!
============================================================
💾 账户已保存到 anymodel_accounts/anymodel_accounts_20250607.json (总计: 1 个)

🎉 注册完成!
```

## 🛡️ 安全特性

### 网络重试机制
- 注册请求：最多重试3次
- 邮件获取：最多重试10次
- 邮件详情：最多重试3次
- 登录验证：最多重试3次

### 智能延迟
- 基础延迟 + 2-5秒随机延迟
- 避免触发网站限流
- 可以随时中断等待

### 错误恢复
- 网络超时自动重试
- 连接错误自动重试
- 详细的错误日志

## 📁 输出文件

成功注册的账户会保存到：
- 目录：`anymodel_accounts/`
- 文件：`anymodel_accounts_YYYYMMDD.json`

每个账户包含：
- 邮箱地址
- 密码
- 用户ID
- 访问Token
- 验证码

## ⚠️ 注意事项

1. **网络环境**: 确保网络连接稳定
2. **请求频率**: 建议延迟10-15秒避免限流
3. **中断操作**: 可以随时按 Ctrl+C 中断
4. **邮箱服务**: 依赖 tempmail.plus 的可用性

## 🔍 故障排除

### SSL连接错误解决方案
**问题**: `[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol`
**解决**:
- ✅ 已在脚本中自动修复
- ✅ 禁用了SSL验证
- ✅ 添加了自定义传输层
- ✅ 如仍有问题，检查防火墙设置

### 邮件获取问题
**问题**: 无法获取验证邮件
**解决**:
- ✅ 现在使用固定邮箱 `<EMAIL>`
- ✅ 脚本会自动重试10次
- ✅ 检查 tempmail.plus 是否可访问
- ✅ 查看详细日志了解具体问题

### 网络连接问题
**问题**: 请求超时或连接失败
**解决**:
- ✅ 脚本会自动重试3次
- ✅ 检查网络连接稳定性
- ✅ 尝试增加延迟时间
- ✅ 查看详细错误日志

### 验证码提取失败
**问题**: 无法从邮件中提取验证码
**解决**:
- ✅ 脚本支持多种验证码格式
- ✅ 会显示完整邮件内容供检查
- ✅ 检查邮件是否来自正确发件人
- ✅ 手动查看邮件内容确认格式

### 如果需要中断
- 按 Ctrl+C 即可安全中断
- 已注册的账户会被保存
- 可以随时重新开始
- 详细日志会显示中断位置

## ✅ 测试通过

脚本已通过以下测试：
- ✅ 基础功能测试
- ✅ 网络连接测试  
- ✅ 中断处理测试
- ✅ 错误恢复测试

现在可以安全使用！
