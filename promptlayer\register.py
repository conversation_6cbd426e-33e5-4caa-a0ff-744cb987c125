# pip install selenium requests Faker
# 不走协议注册，当然是为了让佬一分钱都不花啊...

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import requests
import json
import random
import string
from faker import Faker
import time
import multiprocessing
from multiprocessing import Manager
import logging
import os
import re

fk = Faker()
## 临时邮箱API Key
Key = ""


def get_domains() -> list[str]:
    url = "https://chat-tempmail.com/api/email/domains"

    headers = {"X-API-Key": Key}

    response = requests.get(url, headers=headers)
    domains = response.json()["domains"]
    return domains


Domains = get_domains()


def generate_email(domain: list[str]) -> tuple[str, str]:

    url = "https://chat-tempmail.com/api/emails/generate"

    headers = {"X-API-Key": Key, "Content-Type": "application/json"}
    payload = {
        "name": fk.last_name() + f"{random.randint(0, 9999):04d}",
        "expiryTime": 3600000,
        "domain": random.choice(domain),
    }

    response = requests.post(url, headers=headers, json=payload)
    data = response.json()
    email_id = data["id"]
    email = data["email"]
    return email_id, email


def get_emails(email_id: str) -> list[str]:
    url = f"https://chat-tempmail.com/api/emails/{email_id}"
    headers = {"X-API-Key": Key}
    response = requests.get(url, headers=headers)
    data = response.json()
    messages = data["messages"]
    return messages


def get_email_details(email_id: str, message_id: str) -> list[str]:
    """
        {
      "message": {
        "id": "fd13a8df-1465-4fbc-a612-ca7311c31ff2",
        "from_address": "<EMAIL>",
        "subject": "Test Message 1 - xJOK2h",
        "content": "Test Message 1\n\nThis is test message 1 content.\n\nBest regards,\nSender 1",
        "html": "<div>\n<h1>Test Message 1</h1>\n </div>",
        "received_at": 1745224245084
      }
    }
    """
    url = f"https://chat-tempmail.com/api/emails/{email_id}/{message_id}"
    headers = {"X-API-Key": Key}
    response = requests.get(url, headers=headers)
    data = response.json()
    message = data["message"]
    return message


def extract_verification_code(html: str) -> str:
    """
    从验证邮件的HTML内容中提取验证码

    Args:
        html: 邮件的HTML内容

    Returns:
        str: 提取到的验证码，如果未找到则返回None
    """
    # 尝试匹配新的HTML格式中的验证码
    pattern = r'<span style="[^"]*">\s*(\d{6})\s*</span>'
    match = re.search(pattern, html)
    
    if match:
        return match.group(1)
    
    return None


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(processName)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def generate_random_name():
    """生成随机的美国人名"""
    fake = Faker("en_US")
    return fake.name()


def generate_random_email(name=None):
    """生成随机的电子邮箱"""
    fake = Faker()
    if name:
        # 基于姓名生成邮箱前缀
        name_parts = name.lower().split()
        prefix = name_parts[0] + (name_parts[-1][0] if len(name_parts) > 1 else "")
        prefix += str(random.randint(1, 99999))
    else:
        prefix = fake.user_name() + str(random.randint(1, 9999))

    domains = [
        "gmail.com",
        "outlook.com",
        "yahoo.com",
        "hotmail.com",
        "icloud.com",
        "ucl.edu",
        "standford.edu",
    ]
    domain = random.choice(domains)

    return f"{prefix}@{domain}"


def generate_random_password(length=12):
    """生成随机密码，包含大小写字母、数字和特殊字符"""
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*()-_=+[]{}|;:,.<>?"

    # 确保密码至少包含每种字符
    pwd = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special),
    ]

    # 填充剩余长度
    remaining_length = length - len(pwd)
    all_chars = lowercase + uppercase + digits + special
    pwd.extend(random.choice(all_chars) for _ in range(remaining_length))

    # 打乱密码顺序
    random.shuffle(pwd)
    return "".join(pwd)


def generate_random_user():
    """生成随机用户信息，包括姓名、邮箱和密码"""
    name = generate_random_name()
    email = generate_random_email(name)
    password = generate_random_password()

    return {"name": name, "email": email, "password": password}


def login(email, password):
    """登录并获取访问令牌"""
    try:
        url = "https://api.promptlayer.com/login"
        data = {"email": email, "password": password}
        response = requests.post(url, json=data, timeout=10)
        response.raise_for_status()  # 检查 HTTP 错误
        return response.json()
    except (requests.RequestException, json.JSONDecodeError) as e:
        logger.error(f"登录失败 {email}: {str(e)}")
        return {"error": str(e)}


def save_account_info(account_info):
    """安全地将账户信息保存到文件"""
    try:
        with open("promptlayer.txt", "a+", encoding="utf-8") as f:
            f.write(f"{account_info}\n")
        return True
    except Exception as e:
        logger.error(f"保存账户信息时出错: {str(e)}")
        return False


def register(_):
    """注册 PromptLayer 账户

    注意：参数 _ 用于接收 pool.map 传入的索引，不使用
    """
    # 临时邮箱

    email_id, email = generate_email(Domains)

    user_info = generate_random_user()
    password = user_info["password"]
    name = user_info["name"]

    driver = None
    process_id = multiprocessing.current_process().name

    try:
        logger.info(f"进程 {process_id} 开始为 {email} 注册账户")

        options = webdriver.ChromeOptions()
        # 添加性能优化选项
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        # 可选：无头模式
        # options.add_argument('--headless')

        driver = webdriver.Chrome(options=options)
        # 设置页面加载超时
        driver.set_page_load_timeout(30)

        # 直接访问注册页面
        driver.get("https://dashboard.promptlayer.com/create-account")

        # 填写表单
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "email"))
        )
        driver.find_element(By.ID, "email").send_keys(email)
        driver.find_element(By.ID, "password").send_keys(password)
        driver.find_element(By.ID, "name").send_keys(name)
        driver.find_element(By.ID, "verifyPassword").send_keys(password)
        time.sleep(1.0)
        
        button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[text()='Create Account']")
                    )
                )
        button.click()

        time.sleep(5.0)
        if "Verification Code" in driver.page_source:
            logger.info(f"{email} 需要验证码")
            # 获取验证码
            messages = get_emails(email_id)
            if messages:
                message = get_email_details(email_id, messages[0]["id"])
                if "html" in message:
                    verification_code = extract_verification_code(message["html"])
                    logger.info(f"{email} 的验证码是: {verification_code}")
                    for i, x in enumerate(verification_code):
                        driver.execute_script(
                            f"return document.getElementsByClassName('flex bg-background px-3 py-2 outline-none hover:border-input focus:ring-0 focus-within:border-blue-500 focus:border-2 focus-within:border-2 !focus:shadow-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 transition-colors transition-border duration-200 box-border h-12 w-12 rounded-lg border border-gray-300 text-center text-xl font-medium focus:border-blue-500 focus:ring-blue-500')[{i}]"
                        ).send_keys(x)

        # 点击创建Verify Email
        attempt_count = 0
        max_attempts = 10
        wait_time = 1.0
        max_wait_time = 15.0

        while "onboarding" not in driver.current_url and attempt_count < max_attempts:
            try:
                button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[text()='Verify Email']")
                    )
                )
                button.click()

                # 等待URL变化或超时
                try:
                    WebDriverWait(driver, wait_time).until(
                        lambda d: "onboarding" in d.current_url
                    )
                except:
                    # 超时后继续循环
                    pass

            except Exception as e:
                logger.warning(
                    f"点击创建账户按钮失败 (尝试 {attempt_count+1}/{max_attempts}): {str(e)}"
                )

            # 更新尝试次数和等待时间
            attempt_count += 1
            # 使用较温和的退避策略
            wait_time = min(1.0 * (1.5**attempt_count), max_wait_time)       

        # 检查是否成功进入 onboarding 页面
        if "onboarding" in driver.current_url:
            logger.info(f"{email} 注册成功，已进入 onboarding 页面")

            # 尝试登录获取 token
            token_data = login(email, password)

            if "access_token" in token_data:
                access_token = token_data["access_token"]
                account_info = f"{email}----{password}----{access_token}"

                # 同步写入文件（每个进程独立写入，不使用共享锁）
                save_result = save_account_info(account_info)
                if save_result:
                    logger.info(f"账户信息已保存: {email}")

                return {
                    "success": True,
                    "email": email,
                    "password": password,
                    "access_token": access_token,
                }
            else:
                logger.warning(
                    f"无法获取访问令牌: {token_data.get('error', '未知错误')}"
                )
        else:
            logger.warning(f"{email} 注册可能失败，未能进入 onboarding 页面")

        return {"success": False, "email": email}

    except Exception as e:
        logger.error(f"注册 {email} 过程中发生错误: {str(e)}")
        return {"success": False, "email": email, "error": str(e)}
    finally:
        # 确保浏览器关闭
        try:
            if driver:
                driver.quit()
        except Exception as e:
            logger.error(f"关闭浏览器时出错: {str(e)}")


def main(total_registrations=10, concurrent_processes=3):
    """主函数：并发执行注册任务"""
    # 确保输出文件目录存在
    output_dir = os.path.dirname(os.path.abspath("promptlayer.txt"))
    os.makedirs(output_dir, exist_ok=True)

    logger.info(
        f"开始并发注册 {total_registrations} 个账户，并发数: {concurrent_processes}"
    )

    # 创建进程池
    with multiprocessing.Pool(processes=concurrent_processes) as pool:
        # 使用range作为简单的任务索引
        results = pool.map(register, range(total_registrations))

    # 统计结果
    success_count = sum(1 for result in results if result.get("success", False))
    logger.info(f"注册任务完成。成功: {success_count}/{total_registrations}")

    # 返回详细结果
    return results


if __name__ == "__main__":

    # Windows 多进程支持
    multiprocessing.freeze_support()

    # 并发数和总注册数
    concurrent_processes = 1  # 调整为适合系统资源的数量
    total_registrations = 1

    main(total_registrations, concurrent_processes)