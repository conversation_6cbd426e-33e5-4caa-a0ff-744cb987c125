#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AnyModel.xyz Token余额校验脚本
"""

import json
import ssl
from pathlib import Path
from datetime import datetime

try:
    import httpx
except ImportError:
    print("❌ 缺少 httpx 库，请运行: pip install httpx")
    exit(1)

class TokenChecker:
    """Token余额检查器"""
    
    def __init__(self):
        self.base_url = "https://app.anymodel.xyz"
        
        # SSL配置
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        self.session = httpx.Client(
            timeout=httpx.Timeout(30.0, connect=15.0, read=30.0),
            verify=False,
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            follow_redirects=True,
            transport=httpx.HTTPTransport(verify=False, retries=3)
        )
        print("🔧 初始化Token检查器")
    
    def get_user_status(self, token):
        """获取用户状态和余额"""
        url = f"{self.base_url}/api/user-status"
        headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'authorization': token,
            'priority': 'u=1, i',
            'referer': 'https://app.anymodel.xyz/',
            'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
        }
        
        try:
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'valid',
                    'username': data.get('username', ''),
                    'plan': data.get('plan', ''),
                    'credits': data.get('credits', 0),
                    'status_info': data.get('status', ''),
                    'saves': data.get('saves', 0),
                    'allowances': data.get('allowances', {}),
                    'data': data
                }
            elif response.status_code == 401:
                return {'status': 'invalid', 'error': 'Token已过期或无效'}
            else:
                return {'status': 'error', 'error': f'HTTP {response.status_code}: {response.text}'}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def check_token(self, token, index=None):
        """检查单个token"""
        prefix = f"[{index+1}] " if index is not None else ""
        print(f"🔍 {prefix}检查Token: {token[:20]}...")
        
        result = self.get_user_status(token)
        
        if result['status'] == 'valid':
            username = result['username']
            credits = result['credits']
            plan = result['plan']
            status_info = result['status_info']
            
            print(f"✅ {prefix}有效 - {username}")
            print(f"   💰 余额: {credits}")
            print(f"   📋 计划: {plan}")
            print(f"   📊 状态: {status_info}")
            
            return True, result
        else:
            error = result.get('error', '未知错误')
            print(f"❌ {prefix}无效 - {error}")
            return False, result
    
    def check_tokens_from_file(self, file_path):
        """从文件检查tokens，支持每行一个或逗号分隔格式"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 首先尝试按逗号分隔
            if ',' in content:
                tokens = [t.strip() for t in content.split(',') if t.strip()]
            else:
                # 如果没有逗号，则按行分割
                tokens = [line.strip() for line in content.splitlines() if line.strip()]
            
            if not tokens:
                print("❌ 文件中没有找到token")
                return
            
            print(f"📋 从文件读取到 {len(tokens)} 个token")
            return self.check_tokens(tokens)
            
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    
    def check_tokens(self, tokens):
        """批量检查tokens"""
        print(f"\n🚀 开始检查 {len(tokens)} 个token")
        print("=" * 60)
        
        valid_tokens = []
        invalid_tokens = []
        
        for i, token in enumerate(tokens):
            is_valid, result = self.check_token(token, i)
            
            if is_valid:
                valid_tokens.append({
                    'token': token,
                    'result': result
                })
            else:
                invalid_tokens.append({
                    'token': token,
                    'result': result
                })
            
            print()  # 空行分隔
        
        # 输出统计结果
        self.print_summary(valid_tokens, invalid_tokens)
        
        # 保存结果到文件
        self.save_results(valid_tokens, invalid_tokens)
        
        return valid_tokens, invalid_tokens
    
    def print_summary(self, valid_tokens, invalid_tokens):
        """打印统计结果"""
        print("=" * 60)
        print("📊 检查结果统计")
        print("=" * 60)
        
        print(f"✅ 有效Token: {len(valid_tokens)} 个")
        if valid_tokens:
            total_credits = sum(token['result']['credits'] for token in valid_tokens)
            print(f"💰 总余额: {total_credits}")
            print("\n📋 有效Token详情:")
            for i, token_info in enumerate(valid_tokens):
                result = token_info['result']
                print(f"  {i+1}. {result['username']} - 余额: {result['credits']} - 计划: {result['plan']}")
        
        print(f"\n❌ 无效Token: {len(invalid_tokens)} 个")
        if invalid_tokens:
            print("\n📋 无效Token详情:")
            for i, token_info in enumerate(invalid_tokens):
                error = token_info['result'].get('error', '未知错误')
                token = token_info['token']
                print(f"  {i+1}. {token[:20]}... - {error}")
    
    def save_results(self, valid_tokens, invalid_tokens):
        """保存结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存有效tokens
        if valid_tokens:
            valid_file = f"valid_tokens_{timestamp}.txt"
            with open(valid_file, "w", encoding="utf-8") as f:
                for token_info in valid_tokens:
                    f.write(token_info['token'] + "\n")
            print(f"\n💾 有效Token已保存到: {valid_file}")
        
        # 保存无效tokens
        if invalid_tokens:
            invalid_file = f"invalid_tokens_{timestamp}.txt"
            with open(invalid_file, "w", encoding="utf-8") as f:
                for token_info in invalid_tokens:
                    f.write(token_info['token'] + "\n")
            print(f"💾 无效Token已保存到: {invalid_file}")
        
        # 保存详细结果
        result_file = f"token_check_result_{timestamp}.json"
        result_data = {
            'check_time': datetime.now().isoformat(),
            'total_tokens': len(valid_tokens) + len(invalid_tokens),
            'valid_count': len(valid_tokens),
            'invalid_count': len(invalid_tokens),
            'valid_tokens': [
                {
                    'token': token_info['token'],
                    'username': token_info['result']['username'],
                    'credits': token_info['result']['credits'],
                    'plan': token_info['result']['plan'],
                    'status': token_info['result']['status_info']
                }
                for token_info in valid_tokens
            ],
            'invalid_tokens': [
                {
                    'token': token_info['token'],
                    'error': token_info['result'].get('error', '未知错误')
                }
                for token_info in invalid_tokens
            ]
        }
        
        with open(result_file, "w", encoding="utf-8") as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        print(f"💾 详细结果已保存到: {result_file}")
    
    def close(self):
        """关闭会话"""
        self.session.close()

def main():
    """主函数"""
    print("🔧 AnyModel.xyz Token余额校验工具")
    print("=" * 50)
    
    print("\n输入模式:")
    print("1. 从文件读取token列表")
    print("2. 手动输入单个token")
    
    try:
        while True:
            try:
                mode = int(input("\n选择模式 (1-2): "))
                if mode in [1, 2]:
                    break
                else:
                    print("请输入1或2。")
            except ValueError:
                print("请输入有效数字。")
        
        checker = TokenChecker()
        
        try:
            if mode == 1:
                # 从文件读取
                file_path = input("请输入token文件路径 (例如: tokens.txt): ").strip()
                if not file_path:
                    print("❌ 文件路径不能为空")
                    return
                
                checker.check_tokens_from_file(file_path)
            
            elif mode == 2:
                # 手动输入单个token
                token = input("请输入token: ").strip()
                if not token:
                    print("❌ Token不能为空")
                    return
                
                checker.check_tokens([token])
        
        finally:
            checker.close()
    
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
