#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AnyModel.xyz 自动注册脚本
简化版本，专门用于 AnyModel.xyz 注册
"""

import json
import random
import string
import time
import re
from pathlib import Path
from datetime import datetime, timedelta
from urllib.parse import quote

try:
    import httpx
except ImportError:
    print("❌ 缺少 httpx 库，请运行: pip install httpx")
    exit(1)

class AnyModelRegistrar:
    """AnyModel.xyz 自动注册器"""
    
    def __init__(self):
        self.base_url = "https://app.anymodel.xyz"
        self.tempmail_url = "https://tempmail.plus"
        # 固定使用的邮箱
        self.fixed_email = "<EMAIL>"
        # 当前注册的邮箱地址，用于邮件校验
        self.current_register_email = None

        print("🔧 初始化 AnyModel 注册器")
        print(f"📧 固定监听邮箱: {self.fixed_email}")

        # 增加超时时间和重试配置，禁用SSL验证避免SSL错误
        import ssl

        # 创建自定义SSL上下文，解决SSL连接问题
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        self.session = httpx.Client(
            timeout=httpx.Timeout(30.0, connect=15.0, read=30.0),
            verify=False,  # 禁用SSL验证避免SSL错误
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            follow_redirects=True,
            # 添加自定义传输以处理SSL问题
            transport=httpx.HTTPTransport(
                verify=False,
                retries=3
            )
        )
        print("✅ HTTP客户端初始化完成")
        
    def generate_random_email(self, length=6):
        """生成随机邮箱地址"""
        letters = string.ascii_lowercase
        username = ''.join(random.choice(letters) for _ in range(length))
        return f"{username}@kiechck.top"
    
    def get_signup_headers(self):
        """获取注册请求头"""
        return {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://app.anymodel.xyz',
            'priority': 'u=1, i',
            'referer': 'https://app.anymodel.xyz/',
            'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    def get_tempmail_headers(self, email):
        """获取临时邮箱请求头"""
        return {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'priority': 'u=1, i',
            'referer': 'https://tempmail.plus/zh/',
            'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
    
    def signup(self, email, password, max_retries=3):
        """发起注册"""
        url = f"{self.base_url}/api/signup"
        headers = self.get_signup_headers()

        data = {
            "email": email,
            "password": password,
            "emailConsent": True,
            "analyticsConsent": True
        }

        for attempt in range(max_retries):
            try:
                print(f"� 注册请求 {attempt + 1}/{max_retries}")

                response = self.session.post(url, headers=headers, json=data)

                if response.status_code == 201:
                    result = response.json()
                    print(f"✅ 注册成功: {result.get('msg', '未知消息')}")
                    return True
                else:
                    print(f"❌ 注册失败: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                    return False

            except httpx.TimeoutException as e:
                print(f"⚠️ 请求超时: {e}")
                if attempt < max_retries - 1:
                    print(f"等待 5 秒后重试...")
                    time.sleep(5)
                    continue
                else:
                    print("❌ 多次超时，注册失败")
                    return False

            except httpx.ConnectError as e:
                print(f"⚠️ 连接错误: {e}")
                if attempt < max_retries - 1:
                    print(f"等待 5 秒后重试...")
                    time.sleep(5)
                    continue
                else:
                    print("❌ 连接失败，注册失败")
                    return False

            except KeyboardInterrupt:
                print("\n❌ 用户中断操作")
                return False

            except Exception as e:
                print(f"❌ 注册请求异常: {e}")
                if attempt < max_retries - 1:
                    print(f"等待 3 秒后重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

        return False
    
    def get_mail_list(self, email, max_attempts=10, wait_seconds=5):
        """获取邮件列表，等待验证邮件"""
        encoded_email = quote(email)
        url = f"{self.tempmail_url}/api/mails?email={encoded_email}&first_id=0&epin="
        headers = self.get_tempmail_headers(email)

        print(f"🔍 监听邮件: {email}")

        for attempt in range(max_attempts):
            try:
                print(f"📡 检查邮件 {attempt + 1}/{max_attempts}")
                response = self.session.get(url, headers=headers)

                if response.status_code == 200:
                    data = response.json()

                    if data.get('result') and data.get('count', 0) > 0:
                        mail_list = data.get('mail_list', [])
                        print(f"📬 收到 {len(mail_list)} 封邮件")

                        # 查找来自 AnyModel 的邮件
                        for mail in mail_list:
                            subject = mail.get('subject', '').lower()
                            from_mail = mail.get('from_mail', '').lower()
                            mail_id = mail.get('mail_id')

                            # 匹配 AnyModel 验证邮件
                            is_anymodel_mail = (
                                'anymodel' in subject or
                                'anymodel' in from_mail or
                                'verification' in subject or
                                'munro-research.com' in from_mail or
                                'code' in subject or
                                'noreply' in from_mail
                            )

                            if is_anymodel_mail:
                                print(f"✅ 找到验证邮件 ID: {mail_id}")
                                return mail_id

                        print(f"⚠️ 未找到验证邮件")
                    else:
                        print(f"📭 暂无邮件")
                else:
                    print(f"❌ 请求失败: {response.status_code}")

                if attempt < max_attempts - 1:
                    print(f"⏳ 等待 {wait_seconds} 秒...")
                    time.sleep(wait_seconds)

            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")
                if attempt < max_attempts - 1:
                    time.sleep(wait_seconds)

        print("❌ 未收到验证邮件")
        return None
    
    def get_mail_detail(self, email, mail_id, max_retries=3):
        """获取邮件详情并提取验证码"""
        encoded_email = quote(email)
        url = f"{self.tempmail_url}/api/mails/{mail_id}?email={encoded_email}&epin="
        headers = self.get_tempmail_headers(email)

        for attempt in range(max_retries):
            try:
                print(f"� 获取邮件详情 {attempt + 1}/{max_retries}")
                response = self.session.get(url, headers=headers)

                if response.status_code == 200:
                    data = response.json()

                    if data.get('result'):
                        # 校验邮件的 to 字段是否匹配当前注册的邮箱
                        mail_to = data.get('to', '')
                        if self.current_register_email and mail_to != self.current_register_email:
                            print(f"⚠️ 邮件收件人不匹配: {mail_to} != {self.current_register_email}")
                            return None

                        print(f"✅ 邮件校验通过: to={mail_to}")

                        # 从邮件内容中提取验证码
                        text_content = data.get('text', '')
                        html_content = data.get('html', '')

                        # 尝试从文本和HTML中提取验证码
                        verification_code = self.extract_verification_code(text_content) or \
                                          self.extract_verification_code(html_content)

                        if verification_code:
                            print(f"✅ 提取验证码: {verification_code}")
                            return verification_code
                        else:
                            print("❌ 未提取到验证码")
                            return None
                    else:
                        print("❌ 邮件详情获取失败")
                        if attempt < max_retries - 1:
                            time.sleep(3)
                            continue
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue

            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                    continue

        return None
    
    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        if not content:
            return None

        # 尝试多种验证码模式
        patterns = [
            r'verification code is ([a-z0-9]{6,10})',
            r'code is ([a-z0-9]{6,10})',
            r'验证码[：:]\s*([a-z0-9]{6,10})',
            r'code[：:]\s*([a-z0-9]{6,10})',
            r'([a-z0-9]{8})',
            r'([a-z0-9]{6,10})',
        ]

        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                code = match.group(1)
                if 6 <= len(code) <= 10 and code.isalnum():
                    return code

        return None
    
    def login_with_verification(self, email, password, verification_code, max_retries=3):
        """使用验证码登录"""
        url = f"{self.base_url}/api/login"
        headers = self.get_signup_headers()

        data = {
            "provider": "AnyModel",
            "credentials": {
                "email": email,
                "password": password
            },
            "verificationCode": verification_code
        }

        for attempt in range(max_retries):
            try:
                print(f"� 登录验证 {attempt + 1}/{max_retries}")

                response = self.session.post(url, headers=headers, json=data)

                if response.status_code == 201:
                    result = response.json()
                    token = result.get('token')
                    user_id = result.get('id')

                    if token:
                        print(f"✅ 登录成功")
                        print(f"   用户ID: {user_id}")
                        print(f"   Token: {token[:20]}...")

                        return {
                            'email': email,
                            'password': password,
                            'user_id': user_id,
                            'token': token,
                            'verification_code': verification_code
                        }
                    else:
                        print("❌ 未获取到token")
                        return None
                else:
                    print(f"❌ 登录失败: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                    return None

            except httpx.TimeoutException as e:
                print(f"⚠️ 登录请求超时: {e}")
                if attempt < max_retries - 1:
                    print("等待 5 秒后重试...")
                    time.sleep(5)
                    continue
                else:
                    print("❌ 多次超时，登录失败")
                    return None

            except httpx.ConnectError as e:
                print(f"⚠️ 登录连接错误: {e}")
                if attempt < max_retries - 1:
                    print("等待 5 秒后重试...")
                    time.sleep(5)
                    continue
                else:
                    print("❌ 连接失败，登录失败")
                    return None

            except KeyboardInterrupt:
                print("\n❌ 用户中断操作")
                return None

            except Exception as e:
                print(f"❌ 登录请求异常: {e}")
                if attempt < max_retries - 1:
                    print("等待 3 秒后重试...")
                    time.sleep(3)
                    continue
                else:
                    return None

        return None
    
    def register_single_account(self):
        """注册单个账户的完整流程"""
        print("\n" + "="*60)
        print("🚀 开始注册流程")
        print("="*60)

        # 记录注册开始时间
        self.registration_start_time = datetime.now()
        print(f"⏰ 注册开始时间: {self.registration_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 1. 生成随机邮箱
        print("\n📝 步骤1: 生成注册邮箱")
        email = self.generate_random_email()
        password = email  # 使用邮箱作为密码
        print(f"📧 注册邮箱: {email}")
        print(f"� 登录密码: {password}")

        # 2. 发起注册
        print("\n📝 步骤2: 发起注册请求")
        print(f"🌐 请求URL: {self.base_url}/api/signup")
        if not self.signup(email, password):
            print("❌ 步骤2失败: 注册请求失败")
            return None
        print("✅ 步骤2完成: 注册请求成功")

        # 3. 等待并获取验证邮件 (使用固定邮箱)
        print("\n📝 步骤3: 获取验证邮件")
        print(f"📬 监听邮箱: {self.fixed_email}")
        print("💡 说明: 所有验证邮件都会转发到此固定邮箱")
        mail_id = self.get_mail_list(self.fixed_email)
        if not mail_id:
            print("❌ 步骤3失败: 未获取到验证邮件")
            return None
        print(f"✅ 步骤3完成: 获取到邮件ID {mail_id}")

        # 4. 获取邮件详情并提取验证码
        print("\n📝 步骤4: 提取验证码")
        verification_code = self.get_mail_detail(self.fixed_email, mail_id)
        if not verification_code:
            print("❌ 步骤4失败: 未能提取验证码")
            return None
        print(f"✅ 步骤4完成: 验证码 {verification_code}")

        # 5. 使用验证码登录
        print("\n📝 步骤5: 验证登录")
        print(f"🌐 请求URL: {self.base_url}/api/login")
        account_info = self.login_with_verification(email, password, verification_code)

        if account_info:
            print("✅ 步骤5完成: 登录验证成功")
            print("\n🎉 注册流程全部完成!")
            print("="*60)
        else:
            print("❌ 步骤5失败: 登录验证失败")
            print("="*60)

        return account_info
    
    def close(self):
        """关闭会话"""
        self.session.close()

def save_account(account_data):
    """保存账户数据"""
    output_dir = Path("anymodel_accounts")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = output_dir / f"anymodel_accounts_{timestamp}.json"
    
    # 读取现有数据
    existing_accounts = []
    if output_file.exists():
        try:
            with open(output_file, "r", encoding="utf-8") as f:
                existing_accounts = json.load(f)
        except:
            existing_accounts = []
    
    # 添加新账户
    existing_accounts.append(account_data)
    
    # 保存回文件
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(existing_accounts, f, indent=2, ensure_ascii=False)
    
    print(f"💾 账户已保存到 {output_file} (总计: {len(existing_accounts)} 个)")

def main():
    """主函数"""
    try:
        print("AnyModel.xyz 自动注册工具")
        print("=" * 30)
        print("提示: 按 Ctrl+C 可以随时中断操作")

        print("\n注册模式:")
        print("1. 注册单个账户")
        print("2. 批量注册账户")

        while True:
            try:
                mode = int(input("\n选择模式 (1-2): "))
                if mode in [1, 2]:
                    break
                else:
                    print("请输入1或2。")
            except ValueError:
                print("请输入有效数字。")
            except KeyboardInterrupt:
                print("\n👋 再见！")
                return
    
        if mode == 1:
            # 单个账户注册
            registrar = AnyModelRegistrar()
            try:
                account_info = registrar.register_single_account()
                if account_info:
                    save_account(account_info)
                    print("\n🎉 注册完成!")
                else:
                    print("\n❌ 注册失败")
            finally:
                registrar.close()

        elif mode == 2:
            # 批量注册
            count = int(input("要注册的账户数量: "))
            delay_seconds = float(input("请求间隔延迟 (秒, 建议10-15): "))

            registrar = AnyModelRegistrar()
            successful_accounts = []

            try:
                for i in range(count):
                    print(f"\n{'='*50}")
                    print(f"正在注册第 {i+1}/{count} 个账户")
                    print(f"{'='*50}")

                    try:
                        account_info = registrar.register_single_account()

                        if account_info:
                            successful_accounts.append(account_info)
                            save_account(account_info)
                            print(f"✅ 第 {i+1} 个账户注册成功!")
                        else:
                            print(f"❌ 第 {i+1} 个账户注册失败")

                    except KeyboardInterrupt:
                        print(f"\n⚠️ 用户中断操作，已注册 {len(successful_accounts)} 个账户")
                        break
                    except Exception as e:
                        print(f"❌ 第 {i+1} 个账户注册过程中出现异常: {e}")

                    # 延迟
                    if i < count - 1:
                        try:
                            random_extra = round(random.uniform(2.0, 5.0), 2)
                            total_delay = delay_seconds + random_extra
                            print(f"⏳ 等待 {total_delay:.2f} 秒后进行下一次注册...")

                            # 分段睡眠，便于中断
                            for _ in range(int(total_delay)):
                                time.sleep(1)
                            if total_delay % 1 > 0:
                                time.sleep(total_delay % 1)

                        except KeyboardInterrupt:
                            print(f"\n⚠️ 用户中断操作，已注册 {len(successful_accounts)} 个账户")
                            break

            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断操作，已注册 {len(successful_accounts)} 个账户")
            finally:
                registrar.close()

            print(f"\n🎉 批量注册完成!")
            print(f"成功注册: {len(successful_accounts)} 个账户")
            print(f"失败: {count - len(successful_accounts)} 个账户")

    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 程序运行出现异常: {e}")

if __name__ == "__main__":
    main()
