// 引入必要的deno模块
import { Application, Router, Context } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

// ===== 配置常量 =====
const CONFIG = {
  ANYMODEL_API_URL: "https://app.anymodel.xyz/api/text",
  SERVER_PORT: 8000,
  STREAM_CHUNK_DELAY: 10, // ms
  REQUEST_TIMEOUT: 30000, // 30秒
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1秒
} as const;

// ===== 模型配置 =====
const SUPPORTED_MODELS = [
  { id: "gpt-4o-mini", internalId: "openai/gpt-4o-mini", owned_by: "openai" },
  { id: "gpt-3.5-turbo", internalId: "openai/gpt-3.5-turbo", owned_by: "openai" },
  { id: "claude-3-5-sonnet-20240620", internalId: "anthropic/claude-3-5-sonnet-20240620", owned_by: "anthropic" },
  { id: "claude-3-5-haiku-20241022", internalId: "anthropic/claude-3-5-haiku-20241022", owned_by: "anthropic" },
  { id: "command-r", internalId: "cohere/command-r", owned_by: "cohere" },
  { id: "grok-2-1212", internalId: "xai/grok-2-1212", owned_by: "xai" },
] as const;

// 生成模型数据（避免重复计算时间戳）
const MODELS_DATA = {
  object: "list",
  data: SUPPORTED_MODELS.map(model => ({
    id: model.id,
    object: "model",
    created: Math.floor(Date.now() / 1000),
    owned_by: model.owned_by
  }))
} as const;

// ===== 类型定义 =====
interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

interface ChatCompletionChoice {
  message: ChatMessage;
  index: number;
  finish_reason: "stop" | "length" | "content_filter" | "tool_calls";
}

interface ChatCompletionResponse {
  id: string;
  object: "chat.completion";
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface StreamChoice {
  delta: {
    role?: "assistant";
    content?: string;
  };
  index: number;
  finish_reason?: "stop" | "length" | "content_filter" | null;
}

interface StreamResponse {
  id: string;
  object: "chat.completion.chunk";
  created: number;
  model: string;
  choices: StreamChoice[];
}

interface AnymodelHistoryItem {
  prompt: string;
  response: string;
  functionCalls: unknown[];
  functionResponses: unknown[];
}

interface AnymodelPayload {
  prompt: string;
  image: null;
  pdf: null;
  options: {
    models: string[];
    generatePromptSummary: boolean;
  };
  history: Record<string, AnymodelHistoryItem[]>;
}

interface ErrorResponse {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

// ===== 工具函数 =====
class ApiError extends Error {
  constructor(
    message: string,
    public status: number = 500,
    public type: string = "api_error",
    public code?: string
  ) {
    super(message);
    this.name = "ApiError";
  }
}

function generateId(): string {
  return `chatcmpl-${crypto.randomUUID().replace(/-/g, '')}`;
}

function getCurrentTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

function extractAnymodelToken(authHeader: string | null): string {
  if (!authHeader) {
    throw new ApiError("Authorization header required", 401, "authentication_error");
  }

  const token = authHeader.replace("Bearer ", "").trim();
  if (!token) {
    throw new ApiError("Valid token required in Authorization header", 401, "authentication_error");
  }

  return token;
}

function isValidModel(modelId: string): boolean {
  return SUPPORTED_MODELS.some(model => model.id === modelId);
}

// 获取模型的内部ID
function getModelInternalId(modelId: string): string {
  const model = SUPPORTED_MODELS.find(model => model.id === modelId);
  if (!model) {
    throw new ApiError(`Model '${modelId}' not found`, 404, "invalid_request_error", "model_not_found");
  }
  return model.internalId;
}

function validateRequest(request: ChatCompletionRequest): void {
  if (!request.model) {
    throw new ApiError("Model is required", 400, "invalid_request_error");
  }

  if (!isValidModel(request.model)) {
    const availableModels = SUPPORTED_MODELS.map(m => m.id).join(", ");
    throw new ApiError(
      `Model '${request.model}' not found. Available models: ${availableModels}`,
      404,
      "invalid_request_error",
      "model_not_found"
    );
  }

  if (!request.messages || request.messages.length === 0) {
    throw new ApiError("No messages provided in the request", 400, "invalid_request_error");
  }
}

async function createStreamResponse(chunks: string[]): Promise<ReadableStream<Uint8Array>> {
  const encoder = new TextEncoder();
  let index = 0;

  return new ReadableStream({
    start(controller) {
      function pump() {
        if (index < chunks.length) {
          controller.enqueue(encoder.encode(chunks[index]));
          index++;
          setTimeout(pump, CONFIG.STREAM_CHUNK_DELAY);
        } else {
          controller.close();
        }
      }
      pump();
    }
  });
}

function buildAnymodelHistory(messages: ChatMessage[]): AnymodelHistoryItem[] {
  const history: AnymodelHistoryItem[] = [];
  const processedMessages = messages.slice(0, -1); // 排除最后一条消息

  for (let i = 0; i < processedMessages.length - 1; i += 2) {
    const userMsg = processedMessages[i];
    const assistantMsg = processedMessages[i + 1];

    // if (userMsg?.role === "user" && assistantMsg?.role === "assistant") {
      history.push({
        prompt: userMsg.content,
        response: assistantMsg.content,
        functionCalls: [],
        functionResponses: []
      });
    // }
  }

  return history;
}

async function callAnymodelAPI(payload: AnymodelPayload, token: string): Promise<any> {
  const headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
    "Accept": "application/json",
    "Content-Type": "application/json",
    "authorization": token,
    "origin": "https://app.anymodel.xyz",
    "referer": "https://app.anymodel.xyz/",
  };

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

  try {
    const response = await fetch(CONFIG.ANYMODEL_API_URL, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = errorText;

      try {
        const errorJson = JSON.parse(errorText);
        errorMessage = errorJson.message || errorJson.error?.message || errorText;
      } catch {
        // 使用原始错误文本
      }

      throw new ApiError(`Anymodel API Error (${response.status}): ${errorMessage}`, 502, "anymodel_api_error");
    }

    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof ApiError) {
      throw error;
    }
    if (error.name === 'AbortError') {
      throw new ApiError("Request timeout", 408, "timeout_error");
    }
    throw new ApiError(`Network error: ${error.message}`, 502, "network_error");
  }
}

// ===== 路由处理器 =====
const router = new Router();

// 模型列表端点
router.get("/v1/models", (ctx: Context) => {
  ctx.response.body = MODELS_DATA;
});

router.get("/models", (ctx: Context) => {
  ctx.response.body = MODELS_DATA;
});

// 健康检查端点
router.get("/health", (ctx: Context) => {
  ctx.response.body = {
    status: "ok",
    service: "anymodel-openai-adapter",
    models: SUPPORTED_MODELS.length,
    timestamp: getCurrentTimestamp()
  };
});

// 主要聊天完成端点
router.post("/v1/chat/completions", async (ctx: Context) => {
  let request: ChatCompletionRequest;
  
  try {
    // 提取和验证token
    const anymodelToken = extractAnymodelToken(ctx.request.headers.get("authorization"));

    // 解析请求体
    request = await ctx.request.body({ type: "json" }).value;

    // 验证请求
    validateRequest(request);

    // 提取当前提示内容
    const currentPrompt = request.messages[request.messages.length - 1].content;

    // 构建历史记录
    const history = buildAnymodelHistory(request.messages);

    // 获取内部模型ID
    const internalModelId = getModelInternalId(request.model);

    // 构建Anymodel API载荷
    const anymodelPayload: AnymodelPayload = {
      prompt: currentPrompt,
      image: null,
      pdf: null,
      options: {
        models: [internalModelId],
        generatePromptSummary: false,
      },
      history: history.length > 0 ? { [internalModelId]: history } : {},
    };

    // 调用Anymodel API并处理响应
    const responseJson = await callAnymodelAPI(anymodelPayload, anymodelToken);

    // 提取响应内容
    let fullContent = "";
    if (responseJson.responses && responseJson.responses.length > 0) {
      const firstResponseItem = responseJson.responses[0];
      if (typeof firstResponseItem === "object" && firstResponseItem !== null) {
        fullContent = firstResponseItem.content || "";
      } else {
        fullContent = String(firstResponseItem || "");
      }
    }

    // 处理流式或非流式响应
    if (request.stream) {
      await handleStreamResponse(ctx, request, fullContent);
    } else {
      await handleNormalResponse(ctx, request, fullContent);
    }

  } catch (error) {
    await handleError(ctx, error, request?.stream || false, request);
  }
});

// ===== 响应处理函数 =====
async function handleStreamResponse(ctx: Context, request: ChatCompletionRequest, content: string): Promise<void> {
  const streamId = generateId();
  const createdTime = getCurrentTimestamp();

  const chunks = [
    `data: ${JSON.stringify({
      id: streamId,
      object: "chat.completion.chunk",
      created: createdTime,
      model: request.model,
      choices: [{
        delta: { role: "assistant" },
        index: 0,
        finish_reason: null
      }]
    } as StreamResponse)}\n\n`,
  ];

  if (content) {
    chunks.push(`data: ${JSON.stringify({
      id: streamId,
      object: "chat.completion.chunk",
      created: createdTime,
      model: request.model,
      choices: [{
        delta: { content },
        index: 0,
        finish_reason: null
      }]
    } as StreamResponse)}\n\n`);
  }

  chunks.push(`data: ${JSON.stringify({
    id: streamId,
    object: "chat.completion.chunk",
    created: createdTime,
    model: request.model,
    choices: [{
      delta: {},
      index: 0,
      finish_reason: "stop"
    }]
  } as StreamResponse)}\n\n`);

  chunks.push("data: [DONE]\n\n");

  ctx.response.headers.set("Content-Type", "text/event-stream");
  ctx.response.headers.set("Cache-Control", "no-cache");
  ctx.response.headers.set("Connection", "keep-alive");
  ctx.response.body = await createStreamResponse(chunks);
}

async function handleNormalResponse(ctx: Context, request: ChatCompletionRequest, content: string): Promise<void> {
  const chatResponse: ChatCompletionResponse = {
    id: generateId(),
    object: "chat.completion",
    created: getCurrentTimestamp(),
    model: request.model,
    choices: [{
      message: {
        role: "assistant",
        content
      },
      index: 0,
      finish_reason: "stop"
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };

  ctx.response.body = chatResponse;
}

async function handleError(ctx: Context, error: unknown, isStream: boolean = false): Promise<void> {
  console.error("API Error:", error);

  if (error instanceof ApiError) {
    ctx.response.status = error.status;

    if (isStream) {
      const errorChunks = [
        `data: ${JSON.stringify({
          error: {
            message: error.message,
            type: error.type,
            code: error.code
          }
        })}\n\n`,
        "data: [DONE]\n\n"
      ];

      ctx.response.headers.set("Content-Type", "text/event-stream");
      ctx.response.body = await createStreamResponse(errorChunks);
    } else {
      ctx.response.body = {
        error: {
          message: error.message,
          type: error.type,
          code: error.code
        }
      };
    }
  } else {
    ctx.response.status = 500;
    const errorMessage = error instanceof Error ? error.message : "Internal server error";

    if (isStream) {
      const errorChunks = [
        `data: ${JSON.stringify({
          error: {
            message: errorMessage,
            type: "internal_error"
          }
        })}\n\n`,
        "data: [DONE]\n\n"
      ];

      ctx.response.headers.set("Content-Type", "text/event-stream");
      ctx.response.body = await createStreamResponse(errorChunks);
    } else {
      ctx.response.body = {
        error: {
          message: errorMessage,
          type: "internal_error"
        }
      };
    }
  }
}

// ===== 应用配置和启动 =====
const app = new Application();

// 错误处理中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  try {
    await next();
  } catch (error) {
    console.error("Unhandled error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      error: {
        message: "Internal server error",
        type: "internal_error"
      }
    };
  }
});

// CORS配置
app.use(oakCors({
  origin: "*",
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
}));

// 请求日志中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  console.log(`${ctx.request.method} ${ctx.request.url} - ${ctx.response.status} - ${ms}ms`);
});

// 路由
app.use(router.routes());
app.use(router.allowedMethods());

// 启动服务器
function printStartupInfo(): void {
  console.log("\n=== Anymodel OpenAI API 转换器 ===");
  console.log(`📦 支持模型: ${SUPPORTED_MODELS.length} 个`);
  console.log("🔗 直接使用你的 anymodel token");
  console.log("\n📡 API端点:");
  console.log("  GET  /models - 查看可用模型");
  console.log("  GET  /v1/models - 查看可用模型 (OpenAI兼容)");
  console.log("  POST /v1/chat/completions - 聊天接口");
  console.log("  GET  /health - 健康检查");
  console.log(`\n🚀 服务启动在: http://localhost:${CONFIG.SERVER_PORT}`);
  console.log("=====================================");
}

async function startServer(): Promise<void> {
  try {
    printStartupInfo();
    await app.listen({ port: CONFIG.SERVER_PORT });
  } catch (error) {
    console.error("Failed to start server:", error);
    // @ts-ignore: Deno global
    Deno.exit(1);
  }
}

// 优雅关闭处理
function setupGracefulShutdown(): void {
  // @ts-ignore: Deno global
  const signals: Deno.Signal[] = ["SIGINT", "SIGTERM"];

  signals.forEach((signal) => {
    // @ts-ignore: Deno global
    Deno.addSignalListener(signal, () => {
      console.log(`\n收到 ${signal} 信号，正在关闭服务器...`);
      // @ts-ignore: Deno global
      Deno.exit(0);
    });
  });
}

// 主函数
// @ts-ignore: import.meta.main
if (import.meta.main) {
  setupGracefulShutdown();
  await startServer();
}